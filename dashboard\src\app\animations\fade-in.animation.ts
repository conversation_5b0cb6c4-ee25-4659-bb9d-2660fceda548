import { trigger, transition, style, animate } from '@angular/animations';

/**
 * Animation that fades in elements from bottom to top
 * Used for container elements and page transitions
 */
export const fadeInAnimation = trigger('fadeIn', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateY(20px)' }),
    animate('300ms ease-in', style({ opacity: 1, transform: 'translateY(0)' }))
  ])
]);

/**
 * Animation that slides in elements from left to right
 * Used for sidebar or navigation elements
 */
export const slideInAnimation = trigger('slideIn', [
  transition(':enter', [
    style({ opacity: 0, transform: 'translateX(-20px)' }),
    animate('400ms ease-out', style({ opacity: 1, transform: 'translateX(0)' }))
  ])
]);

/**
 * Animation that scales in elements with a subtle zoom effect
 * Used for cards and interactive elements
 */
export const scaleInAnimation = trigger('scaleIn', [
  transition(':enter', [
    style({ opacity: 0, transform: 'scale(0.8)' }),
    animate('250ms ease-out', style({ opacity: 1, transform: 'scale(1)' }))
  ])
]);
