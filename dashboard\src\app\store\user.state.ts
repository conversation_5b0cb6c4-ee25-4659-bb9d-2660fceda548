import { User, UserDetail } from '../services/user.service';

/**
 * Interface defining the shape of the user state in NgRx store
 */
export interface UserState {
  /** Array of users for the list view */
  users: User[];

  /** Currently selected user for detail view */
  selectedUser: UserDetail | null;

  /** Loading state indicator */
  loading: boolean;

  /** Error message if any operation fails */
  error: string | null;

  /** Current page number for pagination */
  currentPage: number;

  /** Total number of pages available */
  totalPages: number;

  /** Total number of users in the system */
  totalUsers: number;
}

/**
 * Initial state for the user store
 */
export const initialUserState: UserState = {
  users: [],
  selectedUser: null,
  loading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  totalUsers: 0
};
