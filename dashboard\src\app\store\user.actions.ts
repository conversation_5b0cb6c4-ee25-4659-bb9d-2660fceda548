import { createAction, props } from '@ngrx/store';
import { User, UserDetail, UsersResponse } from '../services/user.service';

// Load Users Actions
export const loadUsers = createAction(
  '[User List] Load Users',
  props<{ page: number; limit: number }>()
);

export const loadUsersSuccess = createAction(
  '[User List] Load Users Success',
  props<{ response: UsersResponse; page: number }>()
);

export const loadUsersFailure = createAction(
  '[User List] Load Users Failure',
  props<{ error: string }>()
);

// Load User Detail Actions
export const loadUserDetail = createAction(
  '[User Detail] Load User Detail',
  props<{ userId: number }>()
);

export const loadUserDetailSuccess = createAction(
  '[User Detail] Load User Detail Success',
  props<{ user: UserDetail }>()
);

export const loadUserDetailFailure = createAction(
  '[User Detail] Load User Detail Failure',
  props<{ error: string }>()
);

// Clear Actions
export const clearSelectedUser = createAction(
  '[User] Clear Selected User'
);

export const clearError = createAction(
  '[User] Clear Error'
);
