import { Directive, ElementRef, HostListener, Input, Renderer2, OnInit } from '@angular/core';

// Enhanced highlight directive with improved visual feedback
// Provides smooth animations, better color transitions, and accessibility features
// Just add appHighlight to any element and it becomes interactive with beautiful effects
@Directive({
  selector: '[appHighlight]',
  standalone: true
})
export class HighlightDirective implements OnInit {
  // You can customize the hover color by setting this
  @Input() highlightColor = 'rgba(99, 102, 241, 0.08)';

  // The normal background color when not hovering
  @Input() defaultColor = 'transparent';

  // Custom scale factor for hover effect
  @Input() scaleAmount = '1.03';

  // Animation duration
  @Input() animationDuration = '0.3s';

  // Shadow intensity on hover
  @Input() shadowIntensity = 'medium';

  private originalBoxShadow: string = '';
  private isHovered = false;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit() {
    // Store original box shadow
    this.originalBoxShadow = getComputedStyle(this.el.nativeElement).boxShadow;

    // Set initial transition
    this.renderer.setStyle(this.el.nativeElement, 'transition', `all ${this.animationDuration} cubic-bezier(0.4, 0, 0.2, 1)`);

    // Ensure the element is focusable for accessibility
    if (!this.el.nativeElement.getAttribute('tabindex')) {
      this.renderer.setAttribute(this.el.nativeElement, 'tabindex', '0');
    }
  }

  /**
   * Handle mouse enter event
   * Applies highlight color, scale effect, and enhanced shadow
   */
  @HostListener('mouseenter') onMouseEnter() {
    this.isHovered = true;
    this.applyHoverEffects();
  }

  /**
   * Handle mouse leave event
   * Removes highlight color, scale effect, and shadow
   */
  @HostListener('mouseleave') onMouseLeave() {
    this.isHovered = false;
    this.removeHoverEffects();
  }

  /**
   * Handle focus event for accessibility
   */
  @HostListener('focus') onFocus() {
    if (!this.isHovered) {
      this.applyFocusEffects();
    }
  }

  /**
   * Handle blur event for accessibility
   */
  @HostListener('blur') onBlur() {
    if (!this.isHovered) {
      this.removeHoverEffects();
    }
  }

  /**
   * Apply all hover effects
   */
  private applyHoverEffects() {
    this.highlight(this.highlightColor);
    this.addScale();
    this.addShadow();
    this.addBorderGlow();
  }

  /**
   * Apply focus effects (similar to hover but with different styling)
   */
  private applyFocusEffects() {
    this.highlight('rgba(99, 102, 241, 0.05)');
    this.renderer.setStyle(this.el.nativeElement, 'outline', '2px solid rgba(99, 102, 241, 0.5)');
    this.renderer.setStyle(this.el.nativeElement, 'outline-offset', '2px');
  }

  /**
   * Remove all hover effects
   */
  private removeHoverEffects() {
    this.highlight(this.defaultColor);
    this.removeScale();
    this.removeShadow();
    this.removeBorderGlow();
    this.renderer.removeStyle(this.el.nativeElement, 'outline');
    this.renderer.removeStyle(this.el.nativeElement, 'outline-offset');
  }

  /**
   * Apply background color to the element
   */
  private highlight(color: string) {
    this.renderer.setStyle(this.el.nativeElement, 'background-color', color);
  }

  /**
   * Add scale effect with smooth animation
   */
  private addScale() {
    this.renderer.setStyle(this.el.nativeElement, 'transform', `scale(${this.scaleAmount}) translateY(-2px)`);
  }

  /**
   * Remove scale effect
   */
  private removeScale() {
    this.renderer.setStyle(this.el.nativeElement, 'transform', 'scale(1) translateY(0)');
  }

  /**
   * Add enhanced shadow effect
   */
  private addShadow() {
    let shadowValue = '';

    switch (this.shadowIntensity) {
      case 'light':
        shadowValue = '0 4px 12px rgba(0, 0, 0, 0.1)';
        break;
      case 'medium':
        shadowValue = '0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(99, 102, 241, 0.1)';
        break;
      case 'heavy':
        shadowValue = '0 12px 35px rgba(0, 0, 0, 0.2), 0 6px 15px rgba(99, 102, 241, 0.15)';
        break;
      default:
        shadowValue = '0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(99, 102, 241, 0.1)';
    }

    this.renderer.setStyle(this.el.nativeElement, 'box-shadow', shadowValue);
  }

  /**
   * Remove shadow effect
   */
  private removeShadow() {
    this.renderer.setStyle(this.el.nativeElement, 'box-shadow', this.originalBoxShadow);
  }

  /**
   * Add subtle border glow effect
   */
  private addBorderGlow() {
    this.renderer.setStyle(this.el.nativeElement, 'border-color', 'rgba(99, 102, 241, 0.3)');
  }

  /**
   * Remove border glow effect
   */
  private removeBorderGlow() {
    this.renderer.removeStyle(this.el.nativeElement, 'border-color');
  }
}
