# 🚀 Dashboard - User Management System

A modern, responsive user management dashboard built with Angular 18+ featuring real-time data, beautiful animations, and state-of-the-art user experience.

![Dashboard Preview](https://img.shields.io/badge/Angular-18+-red?style=for-the-badge&logo=angular)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue?style=for-the-badge&logo=typescript)
![Material Design](https://img.shields.io/badge/Material_Design-UI-purple?style=for-the-badge&logo=material-design)

## ✨ Features

### 🎯 Core Functionality

- **User Management** - Browse, search, and view detailed user profiles
- **Smart Search** - Search by name or ID with instant results
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **Real-time Data** - Live data from DummyJSON API
- **Smooth Navigation** - Seamless routing between pages

### 🎨 User Experience

- **Modern UI** - Clean, professional interface with Material Design
- **Beautiful Animations** - Smooth transitions and hover effects
- **Loading States** - Professional loading indicators
- **Error Handling** - Graceful error messages and fallbacks

### ⚡ Technical Features

- **NgRx State Management** - Predictable state management
- **RxJS Observables** - Reactive programming patterns
- **Custom Directives** - Reusable UI behaviors
- **TypeScript** - Type-safe development
- **SCSS Styling** - Modern CSS with variables and mixins

## 🛠️ Tech Stack

| Technology           | Purpose              | Version |
| -------------------- | -------------------- | ------- |
| **Angular**          | Frontend Framework   | 18+     |
| **TypeScript**       | Programming Language | 5.0+    |
| **NgRx**             | State Management     | 18+     |
| **Angular Material** | UI Components        | 18+     |
| **RxJS**             | Reactive Programming | 7+      |
| **SCSS**             | Styling              | Latest  |

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Angular CLI 18+

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd task-Abudiyab/user-dashboard
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   ng serve
   ```

4. **Open your browser**
   ```
   http://localhost:4200
   ```

## 📱 Usage

### Navigation

- **Home Page** - View all users in a beautiful grid layout
- **User Details** - Click any user card to see detailed information
- **Search** - Use the search bar to find users by name or ID
- **Back Navigation** - Easy return to user list from any page

### Search Functionality

- **By Name** - Type any part of a user's name
- **By ID** - Enter a user ID number for direct access
- **Instant Results** - Real-time search as you type

## 🏗️ Project Structure

```
src/app/
├── components/           # UI Components
│   ├── header/          # Navigation and search
│   ├── user-list/       # User grid with pagination
│   └── user-detail/     # Individual user profiles
├── services/            # Business Logic
│   └── user.service.ts  # API integration
├── store/              # NgRx State Management
│   ├── user.actions.ts # Action definitions
│   ├── user.reducer.ts # State reducers
│   ├── user.effects.ts # Side effects
│   └── user.selectors.ts # State selectors
├── directives/         # Custom Directives
│   └── highlight.directive.ts
└── animations/         # Animation Definitions
    └── fade-in.animation.ts
```

## 🎨 Design System

### Color Palette

- **Primary** - `#6366f1` (Indigo)
- **Secondary** - `#10b981` (Emerald)
- **Accent** - `#f59e0b` (Amber)
- **Background** - `#f8fafc` (Slate)
- **Surface** - `#ffffff` (White)

### Typography

- **Font Family** - Inter, Segoe UI
- **Headings** - 700 weight, tight spacing
- **Body** - 400 weight, comfortable line height

## 🔧 Development

### Available Scripts

```bash
# Development server
ng serve

# Build for production
ng build

# Run tests
ng test

# Code linting
ng lint

# Generate component
ng generate component <name>
```

### Code Style

- **TypeScript** - Strict mode enabled
- **ESLint** - Code quality enforcement
- **Prettier** - Code formatting
- **Angular Style Guide** - Official conventions

## 📊 Performance

- **Lazy Loading** - Components loaded on demand
- **OnPush Strategy** - Optimized change detection
- **Tree Shaking** - Unused code elimination
- **Bundle Optimization** - Minimal bundle sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Your Name**

- GitHub: [@yourusername](https://github.com/yourusername)
- LinkedIn: [Your LinkedIn](https://linkedin.com/in/yourprofile)

## 🙏 Acknowledgments

- [Angular Team](https://angular.io) for the amazing framework
- [Material Design](https://material.io) for the design system
- [DummyJSON](https://dummyjson.com) for the API
- [NgRx Team](https://ngrx.io) for state management

---

⭐ **Star this repository if you found it helpful!**
task for technical assessment
