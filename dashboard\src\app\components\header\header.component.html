<header class="header">
  <div class="container">
    <div class="header-content">
      <h1 class="logo">Dashboard</h1>
      <div class="search-container">
        <!-- Simple Help Tip -->
        <div class="search-tip" *ngIf="showSearchHelp">
          <div class="tip-content">
            <p>� Just type a name like "<PERSON>" or a number like "5"</p>
            <button type="button" class="tip-close" (click)="hideSearchHelp()">OK</button>
          </div>
        </div>

        <div class="search-box">
          <input
            type="text"
            placeholder="Search users by name or ID"
            class="search-input"
            [(ngModel)]="searchQuery"
            (keyup.enter)="onSearchEnter()"
            (input)="onSearchInput($event)"
            (focus)="onSearchFocus()"
            autocomplete="off"
          />

          <button
            type="button"
            class="search-btn"
            (click)="onSearchEnter()"
            title="Search"
            [disabled]="!searchQuery || searchQuery.trim().length === 0"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" *ngIf="!isSearching">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <div class="spinner" *ngIf="isSearching"></div>
          </button>

          <button
            type="button"
            class="clear-btn"
            (click)="clearSearch()"
            title="Clear"
            *ngIf="searchQuery && searchQuery.trim().length > 0"
          >
            ✕
          </button>


        </div>
        <!-- Search Results -->
        <div class="results" *ngIf="showSearchResults && searchResults.length > 0">
          <div
            class="result-item"
            *ngFor="let user of searchResults"
            (click)="selectUser(user.id)"
          >
            <img [src]="user.image" [alt]="user.firstName + ' ' + user.lastName" class="user-pic" />
            <div class="user-info">
              <div
                class="user-name"
                [innerHTML]="highlightText(user.firstName + ' ' + user.lastName, searchQuery)"
              ></div>
              <div class="user-id">
                ID: <span [innerHTML]="highlightText(user.id.toString(), searchQuery)"></span>
              </div>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div class="no-results" *ngIf="showSearchResults && searchResults.length === 0 && searchQuery && searchQuery.trim().length > 0 && !isSearching">
          <p>No users found for "{{ searchQuery }}"</p>
          <button type="button" (click)="clearSearch()">Try again</button>
        </div>

        <!-- Loading -->
        <div class="loading" *ngIf="isSearching">
          <p>Searching...</p>
        </div>
      </div>
    </div>
  </div>
</header>
